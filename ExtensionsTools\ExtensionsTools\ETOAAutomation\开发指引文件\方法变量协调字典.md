# 🗂️ ETOAAutomation - Public方法/变量协调字典

## 🎯 阶段说明与类对应关系
- **第一阶段**: 基础架构搭建 ⏳待开始
  - 涉及类：Models下的所有数据模型类
- **第二阶段**: 登录认证模块 ⏳待开始
  - 涉及类：ETOALoginBrowser（窗体类）、ETOAAuthStorage
- **第三阶段**: API交互模块 ⏳待开始
  - 涉及类：ETOAApiClient、相关Helper类
- **第四阶段**: 文件上传模块 ⏳待开始
  - 涉及类：ETOAFileUploader
- **第五阶段**: 模拟操作浏览器 ⏳待开始
  - 涉及类：ETOASimulationBrowser（窗体类）
- **第六阶段**: 会话管理模块 ⏳待开始
  - 涉及类：ETOASessionManager、ETOASessionStorage
- **第七阶段**: 主客户端集成 ⏳待开始
  - 涉及类：ETOAClient（主类）
- **第八阶段**: 文档和示例 ⏳待开始
  - 涉及类：Examples下的示例类

## 📚 字典说明

### 🎯 核心目的
统一管理ETOAAutomation项目多文件间的Public方法和变量命名，避免开发过程中的调用混乱和命名冲突。

### 🔄 使用流程
1. **步骤开始前**: 仔细阅读字典中已有的类、方法、变量定义
2. **开发过程中**: 严格按照字典中的命名和接口进行开发
3. **步骤完成后**: 立即将新增的public方法和变量添加到字典中
4. **命名冲突**: 如发现命名冲突，优先使用字典中已定义的名称

### ⚠️ 重要规则
- **每个步骤开始前必须读取此字典**
- **每个步骤完成后必须更新此字典**
- **严格按照字典中的方法签名进行调用**
- **新增方法必须包含完整的参数和返回值类型**

---

## 📋 类和方法字典

### 1. ETOAClient类 (ETOAClient.cs)

**类描述**: OA系统自动化客户端主类，整合所有功能模块，提供统一接口

#### Public方法
```csharp
public ETOAClient(string baseUrl)
    /// <summary>初始化ETOAClient</summary>

public async Task<bool> LoginAsync(string username, string password)
    /// <summary>登录OA系统</summary>

public async Task<T> GetApiDataAsync<T>(string endpoint)
    /// <summary>获取API数据</summary>

public async Task<T> PostApiDataAsync<T>(string endpoint, object data)
    /// <summary>提交API数据</summary>

public async Task<ETOAUploadResult> UploadFileAsync(string endpoint, string filePath, object formData = null)
    /// <summary>上传文件</summary>

public void Dispose()
    /// <summary>释放资源</summary>

public string GetConfig(string section, string key, string defaultValue = "")
    /// <summary>获取配置值</summary>

public void SetConfig(string section, string key, string value)
    /// <summary>设置配置值</summary>
```

#### Public属性
```csharp
public string BaseUrl { get; set; }  // OA系统基础URL
public bool IsLoggedIn { get; }      // 登录状态
public ETOALoginInfo LoginInfo { get; } // 登录信息
```

---

### 2. ETOALoginBrowser类 (ETOALoginBrowser.cs)

**类描述**: 基于CefSharp的OA系统登录浏览器，专门处理登录认证
**窗体文件**: 包含ETOALoginBrowser.cs、ETOALoginBrowser.Designer.cs、ETOALoginBrowser.resx

#### Public方法
```csharp
public ETOALoginBrowser(string loginUrl)
    /// <summary>初始化登录浏览器</summary>
    /// <param name="loginUrl">登录URL</param>

public async Task<ETOALoginInfo> ShowLoginDialogAsync()
    /// <summary>显示登录对话框并获取认证信息</summary>
    /// <returns>登录信息</returns>

public async Task<bool> AutoLoginAsync(string username, string password)
    /// <summary>自动登录</summary>
    /// <param name="username">用户名</param>
    /// <param name="password">密码</param>
    /// <returns>登录是否成功</returns>

public ETOALoginInfo GetLoginInfo()
    /// <summary>获取登录信息</summary>
    /// <returns>登录信息</returns>

public void Close()
    /// <summary>关闭浏览器</summary>

public void InitializeComponent()
    /// <summary>初始化窗体组件（Designer.cs中实现）</summary>
```

#### Public属性
```csharp
public string LoginUrl { get; set; }     // 登录URL
public bool IsLoginSuccessful { get; }   // 登录是否成功
public Dictionary<string, string> Cookies { get; } // Cookie信息
```

#### 窗体相关属性
```csharp
public ChromiumWebBrowser Browser { get; }  // CefSharp浏览器控件
public Button BtnClose { get; }             // 关闭按钮
public Label LblStatus { get; }             // 状态标签
public ProgressBar ProgressBar { get; }     // 进度条
```

---

### 3. ETOAApiClient类 (ETOAApiClient.cs)

**类描述**: 基于Flurl.Http的API交互客户端，处理所有HTTP请求

#### Public方法
```csharp
public ETOAApiClient(string baseUrl)
    /// <summary>初始化API客户端</summary>

public void SetAuthenticationInfo(ETOALoginInfo loginInfo)
    /// <summary>设置认证信息</summary>

public async Task<T> GetAsync<T>(string endpoint)
    /// <summary>GET请求</summary>

public async Task<T> GetAsync<T>(string endpoint, object queryParams = null, int retryCount = -1)
    /// <summary>GET请求（带查询参数和重试）</summary>

public async Task<T> PostAsync<T>(string endpoint, object data)
    /// <summary>POST请求</summary>

public async Task<T> PostAsync<T>(string endpoint, object data, int retryCount)
    /// <summary>POST请求（带重试）</summary>

public async Task<T> PostFormAsync<T>(string endpoint, object formData, int retryCount = -1)
    /// <summary>POST表单数据请求</summary>

public async Task<T> PutAsync<T>(string endpoint, object data)
    /// <summary>PUT请求</summary>

public async Task<T> PutAsync<T>(string endpoint, object data, int retryCount)
    /// <summary>PUT请求（带重试）</summary>

public async Task<bool> DeleteAsync(string endpoint)
    /// <summary>DELETE请求</summary>

public async Task<bool> DeleteAsync(string endpoint, int retryCount)
    /// <summary>DELETE请求（带重试）</summary>

public async Task<List<T>> BatchGetAsync<T>(IEnumerable<string> endpoints)
    /// <summary>批量GET请求</summary>

public void ClearCache()
    /// <summary>清除请求缓存</summary>

public void ClearExpiredCache()
    /// <summary>清除过期缓存</summary>

public (int TotalItems, int ExpiredItems) GetCacheStats()
    /// <summary>获取缓存统计信息</summary>

public async Task<bool> TestConnectionAsync(string endpoint = "")
    /// <summary>测试连接</summary>

public void Dispose()
    /// <summary>释放资源</summary>
```

#### Public属性
```csharp
public string BaseUrl { get; set; }      // API基础URL
public int TimeoutSeconds { get; set; }  // 超时时间
public bool IsAuthenticated { get; }     // 是否已认证
public int MaxConcurrentRequests { get; set; }  // 最大并发请求数
public bool EnableRequestCache { get; set; }    // 是否启用请求缓存
public int CacheExpirationMinutes { get; set; } // 缓存过期时间（分钟）
public int DefaultRetryCount { get; set; }      // 默认重试次数
public int RetryIntervalMs { get; set; }        // 重试间隔（毫秒）
```

---

### 4. ETOASessionManager类 (ETOASessionManager.cs)

**类描述**: 会话状态管理器，负责维护登录状态和定期刷新

#### Public方法
```csharp
public ETOASessionManager(ETOAApiClient apiClient)
    /// <summary>初始化会话管理器</summary>

public async Task<bool> StartSessionMonitoringAsync()
    /// <summary>开始会话监控</summary>

public void StopSessionMonitoring()
    /// <summary>停止会话监控</summary>

public async Task<bool> RefreshSessionAsync()
    /// <summary>刷新会话</summary>

public async Task<bool> IsSessionValidAsync()
    /// <summary>检查会话是否有效</summary>
```

#### Public属性
```csharp
public bool IsMonitoring { get; }        // 是否正在监控
public DateTime LastHeartbeat { get; }   // 最后心跳时间
public int HeartbeatInterval { get; set; } // 心跳间隔（秒）
```

---

### 5. ETOAFileUploader类 (ETOAFileUploader.cs)

**类描述**: 文件上传处理器，支持单文件和批量文件上传

#### Public方法
```csharp
public ETOAFileUploader(ETOAApiClient apiClient)
    /// <summary>初始化文件上传器</summary>

public async Task<ETOAUploadResult> UploadFileAsync(string endpoint, string filePath, object formData = null)
    /// <summary>上传单个文件</summary>

public async Task<List<ETOAUploadResult>> UploadFilesAsync(string endpoint, string[] filePaths, object formData = null)
    /// <summary>批量上传文件</summary>

public void SetProgressCallback(Action<int> progressCallback)
    /// <summary>设置进度回调</summary>
```

#### Public属性
```csharp
public int MaxFileSize { get; set; }     // 最大文件大小（MB）
public string[] AllowedExtensions { get; set; } // 允许的文件扩展名
```

---

### 6. ETOASimulationBrowser类 (ETOASimulationBrowser.cs)

**类描述**: 模拟操作浏览器窗体，提供网页自动化操作功能
**窗体文件**: 包含ETOASimulationBrowser.cs、ETOASimulationBrowser.Designer.cs、ETOASimulationBrowser.resx

#### Public方法
```csharp
public ETOASimulationBrowser(string url = "")
    /// <summary>初始化模拟操作浏览器</summary>

public async Task NavigateAsync(string url)
    /// <summary>导航到指定URL</summary>

public async Task<bool> FillTextAsync(string selector, string text)
    /// <summary>填写文本框（DOM操作方式）</summary>

public async Task<bool> ClickElementAsync(string selector)
    /// <summary>点击元素（DOM操作方式）</summary>

public async Task<bool> SelectOptionAsync(string selector, string value)
    /// <summary>选择下拉框选项（DOM操作方式）</summary>

public async Task<bool> ClickAtAsync(int x, int y)
    /// <summary>在指定坐标点击（坐标操作方式）</summary>

public async Task<bool> SendKeysAsync(string keys)
    /// <summary>发送键盘输入（坐标操作方式）</summary>

public async Task<bool> WaitForElementAsync(string selector, int timeoutMs = 10000)
    /// <summary>等待元素出现</summary>

public void InitializeComponent()
    /// <summary>初始化窗体组件（Designer.cs中实现）</summary>
```

#### Public属性
```csharp
public string CurrentUrl { get; }        // 当前URL
public bool IsPageLoaded { get; }        // 页面是否加载完成
```

#### 窗体相关属性
```csharp
public ChromiumWebBrowser Browser { get; }     // CefSharp浏览器控件
public TextBox TxtUrl { get; }                 // URL输入框
public Button BtnNavigate { get; }             // 导航按钮
public Button BtnBack { get; }                 // 后退按钮
public Button BtnForward { get; }              // 前进按钮
public Button BtnRefresh { get; }              // 刷新按钮
public Label LblStatus { get; }                // 状态标签
public ProgressBar ProgressBar { get; }        // 进度条
public Panel PanelControls { get; }            // 控制面板
public MenuStrip MenuStrip { get; }            // 菜单栏
```

#### 事件定义
```csharp
public event EventHandler PageLoadCompleted;  // 页面加载完成事件
public event EventHandler<string> ApiDataReceived; // API数据获取完成事件
public event EventHandler<Exception> OperationError; // 操作错误事件
```

#### 核心私有方法（已实现）
```csharp
private void InitializeBrowser()
    /// <summary>初始化浏览器控件</summary>

private void InitializeTimer()
    /// <summary>初始化定时器</summary>

private async Task CheckLoginStatusAsync()
    /// <summary>检查登录状态（异步版本）</summary>

private async Task ExtractLoginInfoAsync()
    /// <summary>提取登录信息（异步版本）</summary>

private async Task<Dictionary<string, string>> GetAllCookiesAsync()
    /// <summary>获取所有Cookie</summary>

private async Task<Dictionary<string, string>> GetUserInfoFromPageAsync()
    /// <summary>从页面获取用户信息</summary>

private async Task TryAutoFillLoginForm()
    /// <summary>尝试自动填写登录表单</summary>

private async Task TryClickLoginButton()
    /// <summary>尝试点击登录按钮</summary>

private bool IsLoginSuccessUrl(string url)
    /// <summary>判断URL是否表示登录成功</summary>
```

#### 辅助类
```csharp
public class CookieVisitor : ICookieVisitor
    /// <summary>Cookie访问器，用于获取浏览器中的所有Cookie</summary>
    /// <property>List<Cookie> Cookies</property>
    /// <property>Task Task</property>
    /// <method>bool Visit(Cookie cookie, int count, int total, ref bool deleteCookie)</method>

public class ETOAException : ETException
    /// <summary>ETOAException异常类</summary>
    /// <nested>ErrorCodes常量类</nested>
```

---

### 7. ETOAAuthStorage类 (Storage/ETOAAuthStorage.cs)

**类描述**: 认证信息存储类，负责安全存储和管理用户认证信息

#### Public方法
```csharp
public ETOAAuthStorage()
    /// <summary>初始化认证信息存储</summary>

public void SaveAuthInfo(ETOALoginInfo loginInfo, string username)
    /// <summary>保存认证信息</summary>
    /// <param name="loginInfo">登录信息</param>
    /// <param name="username">用户名（用作存储键）</param>

public ETOALoginInfo LoadAuthInfo(string username)
    /// <summary>加载认证信息</summary>
    /// <param name="username">用户名</param>
    /// <returns>登录信息，未找到返回null</returns>

public void DeleteAuthInfo(string username)
    /// <summary>删除认证信息</summary>
    /// <param name="username">用户名</param>

public List<string> GetSavedUsernames()
    /// <summary>获取所有已保存的用户名列表</summary>
    /// <returns>用户名列表</returns>

public bool HasAuthInfo(string username)
    /// <summary>检查用户认证信息是否存在</summary>
    /// <param name="username">用户名</param>
    /// <returns>是否存在</returns>

public void CleanExpiredAuthInfo(int expireDays = 30)
    /// <summary>清理过期的认证信息</summary>
    /// <param name="expireDays">过期天数，默认30天</param>
```

#### Private字段
```csharp
private readonly string _configPath;  // 配置文件路径
private const string AUTH_SECTION = "Authentication";  // 认证配置节名称
```

#### 核心特性
- **安全存储**: 使用ETIniFile进行本地配置文件存储
- **多用户支持**: 支持多个OA账户的认证信息管理
- **自动过期清理**: 支持自动清理过期的认证信息
- **ExtensionsTools集成**: 完全集成ETIniFile、ETLogManager、ETException
- **错误处理**: 完善的异常处理和日志记录

---

### 8. ETOASessionStorage类 (Storage/ETOASessionStorage.cs)

**类描述**: 会话状态存储类，负责会话数据的持久化存储

#### Public方法
```csharp
public ETOASessionStorage()
    /// <summary>初始化会话存储</summary>

public void SaveSessionData(ETOASessionData sessionData)
    /// <summary>保存会话数据</summary>

public ETOASessionData LoadSessionData(string sessionId)
    /// <summary>加载会话数据</summary>

public void DeleteSessionData(string sessionId)
    /// <summary>删除会话数据</summary>

public List<string> GetAllSessionIds()
    /// <summary>获取所有会话ID列表</summary>

public void CleanExpiredSessions()
    /// <summary>清理过期的会话数据</summary>
```

---

## 🔗 类间调用关系图

### 依赖关系
```
ETOAClient (主客户端)
  ├── ETOALoginBrowser (登录认证)
  ├── ETOAApiClient (API交互)
  │   ├── ETOASessionManager (会话管理)
  │   └── ETOAFileUploader (文件上传)
  └── ETOASimulationBrowser (模拟操作)

ExtensionsTools集成
  ├── ETIniFile (配置管理)
  ├── ETLogManager (日志记录)
  └── ETException (异常处理)

Storage存储模块
  ├── ETOAAuthStorage (认证信息存储)
  └── ETOASessionStorage (会话状态存储)
```

### 调用流程
```
1. ETOAClient 初始化
2. ETOALoginBrowser 执行登录获取认证信息
3. ETOAApiClient 设置认证信息
4. ETOASessionManager 开始会话监控
5. 用户调用各种功能方法
6. ETOASimulationBrowser 执行自动化操作
7. ETOAFileUploader 处理文件上传
```

---

## 📝 更新记录

### 字典更新日志
| 时间 | 步骤 | 更新内容 | 更新者 |
|------|------|----------|--------|
| 2025-08-02 10:24 | 初始化 | 创建基础字典结构，定义核心类接口 | AI |
| 2025-08-02 11:30 | 1.1完成 | 完成目录结构创建和所有核心类基础文件，包含完整的窗体文件结构 | AI |
| 2025-08-02 12:30 | 1.4完成 | 集成ExtensionsTools模块，添加存储类，更新所有类的方法接口 | AI |
| 2025-08-02 14:30 | 2.1完成 | 完成ETOALoginBrowser类核心功能开发，添加自动登录、Cookie提取、认证信息管理等功能 | AI |
| 2025-08-02 15:00 | 2.2-2.3完成 | 完成ETOAAuthStorage类开发，实现认证信息的安全存储和管理功能 | AI |
| 2025-08-02 15:30 | 阶段2完成 | 完成登录认证模块所有功能，包括界面优化和自检，质量优秀 | AI |
| 2025-08-02 16:30 | 3.1进行中 | 开始开发ETOAApiClient类完整功能，添加数据处理、重试机制、缓存功能 | AI |
| 2025-08-02 17:00 | 3.1完成 | 完成ETOAApiClient类核心功能，创建ETOAJsonHelper、ETOACookieHelper、ETOAConfigHelper辅助类 | AI |

### 命名规范
- **类名**: 使用PascalCase (如: ETOAClient)
- **方法名**: 使用PascalCase (如: LoginAsync)
- **属性名**: 使用PascalCase (如: BaseUrl)
- **常量名**: 使用UPPER_CASE (如: DEFAULT_TIMEOUT)
- **私有字段**: 以下划线开头 (如: _apiClient)
- **窗体控件**: 使用匈牙利命名法前缀 (如: BtnClose, TxtUrl, LblStatus)

### 窗体开发规范
- **文件结构**: 每个窗体必须包含.cs、.Designer.cs、.resx三个文件
- **设计器分离**: UI初始化代码必须在Designer.cs中
- **资源管理**: 图标、图片等资源通过resx文件管理
- **控件命名**: 遵循匈牙利命名法，便于识别控件类型

---

## 🚨 AI更新指令

### 每完成一个步骤后必须执行：

1. **读取当前字典**: 确保了解已有的类的方法和变量定义
2. **更新对应类的方法和属性**: 添加新开发的public方法和属性
3. **检查命名冲突**: 确保新增的命名不与现有的冲突
4. **更新调用关系**: 如有新的类间调用关系，更新关系图
5. **记录更新日志**: 在更新记录中添加本次更新的内容

### 更新格式示例：

```csharp
public async Task<bool> MethodName(string param1, int param2 = 0)
{
    /// <summary>
    /// 方法功能描述
    /// </summary>
    /// <param name="param1">参数1描述</param>
    /// <param name="param2">参数2描述，默认值为0</param>
    /// <returns>返回值描述</returns>
}
```

---

## 🚀 第一阶段开发规则

### 📋 第一阶段：基础架构搭建 (2025-08-02 开始)

#### 🎯 开发目标
建立项目基础框架，创建核心类的基础结构，配置依赖包，集成ExtensionsTools模块。

#### 🗂️ 协调字典使用规范（第一阶段专用）

**每个步骤开始前必须执行：**
1. **读取完整字典** - 仔细阅读所有已定义的类、方法、变量
2. **理解现有架构** - 明确类结构和调用关系
3. **规划新类接口** - 设计类的公共接口
4. **避免命名冲突** - 确保新增的命名不与现有的冲突

**每个步骤完成后必须执行：**
1. **立即更新字典** - 将新开发的类完整添加到字典中
2. **详细记录接口** - 包含所有public方法的完整签名和参数说明
3. **更新调用关系** - 如有新的类间调用，更新依赖关系图
4. **记录更新日志** - 在字典更新记录中添加详细的更新内容

#### 🚨 第一阶段强制规则
- **步骤开始**: 必须先读取字典，了解现有类的接口定义
- **开发过程**: 严格按照字典中的方法签名进行调用
- **步骤结束**: 必须先更新字典，再在进度控制文件中标记完成
- **命名统一**: 新增类必须遵循现有的命名规范
- **接口兼容**: 确保新类与现有类的无缝集成
- **窗体文件完整**: 涉及窗体的类必须包含.cs、.Designer.cs、.resx三个文件

#### 📊 第一阶段已完成类
已完成以下核心类的基础结构：
- ✅ ETOAClient (ETOAClient.cs) - 主客户端类，已实现基础架构
- ✅ ETOALoginBrowser (ETOALoginBrowser.cs + .Designer.cs + .resx) - 登录浏览器窗体类
- ✅ ETOAApiClient (ETOAApiClient.cs) - API交互客户端，基于Flurl.Http
- ✅ ETOASessionManager (ETOASessionManager.cs) - 会话管理器
- ✅ ETOAFileUploader (ETOAFileUploader.cs) - 文件上传处理器
- ✅ ETOASimulationBrowser (ETOASimulationBrowser.cs + .Designer.cs + .resx) - 模拟操作浏览器窗体类
- ✅ ETOALoginInfo (Models/ETOALoginInfo.cs) - 登录信息模型
- ✅ ETOAApiRequest (Models/ETOAApiRequest.cs) - API请求模型
- ✅ ETOAApiResponse (Models/ETOAApiResponse.cs) - API响应模型
- ✅ ETOAUploadResult (Models/ETOAUploadResult.cs) - 上传结果模型
- ✅ ETOASessionData (Models/ETOASessionData.cs) - 会话数据模型
- ✅ ETOAAuthStorage (Storage/ETOAAuthStorage.cs) - 认证信息存储类
- ✅ ETOASessionStorage (Storage/ETOASessionStorage.cs) - 会话状态存储类

**🎯 第一阶段基础架构搭建已完成，所有核心类、数据模型和存储模块已创建完毕。**

#### 🔧 ExtensionsTools集成完成情况
- ✅ **ETIniFile** - 已集成到所有需要配置管理的类中
- ✅ **ETLogManager** - 已集成到所有类中，提供统一的日志记录
- ✅ **ETException** - 已集成到所有类中，提供统一的异常处理

---

**📌 重要提醒：此字典是ETOAAutomation多文件协调开发的核心工具，必须严格维护，确保所有开发都基于此字典进行！**

**🚨 第一阶段开发规则：每个步骤开始前必须读取此字典，每个步骤完成后必须立即更新对应类的方法和属性定义！**
